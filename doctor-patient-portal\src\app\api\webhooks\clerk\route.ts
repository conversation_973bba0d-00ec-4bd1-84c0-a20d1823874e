import { NextRequest, NextResponse } from 'next/server';
import { Webhook } from 'svix';
import { headers } from 'next/headers';
import connectDB from '@/lib/db';
import User from '@/models/User';

const webhookSecret = process.env.CLERK_WEBHOOK_SECRET;

export async function POST(req: NextRequest) {
  if (!webhookSecret) {
    throw new Error('Please add CLERK_WEBHOOK_SECRET from Clerk Dashboard to .env or .env.local');
  }

  // Get the headers
  const headerPayload = headers();
  const svix_id = headerPayload.get('svix-id');
  const svix_timestamp = headerPayload.get('svix-timestamp');
  const svix_signature = headerPayload.get('svix-signature');

  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    return new Response('Error occured -- no svix headers', {
      status: 400,
    });
  }

  // Get the body
  const payload = await req.json();
  const body = JSON.stringify(payload);

  // Create a new Svix instance with your secret.
  const wh = new Webhook(webhookSecret);

  let evt: any;

  // Verify the payload with the headers
  try {
    evt = wh.verify(body, {
      'svix-id': svix_id,
      'svix-timestamp': svix_timestamp,
      'svix-signature': svix_signature,
    });
  } catch (err) {
    console.error('Error verifying webhook:', err);
    return new Response('Error occured', {
      status: 400,
    });
  }

  // Handle the webhook
  const eventType = evt.type;
  
  try {
    await connectDB();

    switch (eventType) {
      case 'user.created':
        // When a user signs up, we don't create a profile yet
        // They will complete onboarding to set their role and profile
        console.log('User created:', evt.data.id);
        break;

      case 'user.updated':
        // Update user profile if it exists
        const updatedUser = await User.findOne({ clerkId: evt.data.id });
        if (updatedUser) {
          updatedUser.email = evt.data.email_addresses[0]?.email_address;
          updatedUser.profile.firstName = evt.data.first_name || '';
          updatedUser.profile.lastName = evt.data.last_name || '';
          updatedUser.profile.profileImage = evt.data.image_url || '';
          await updatedUser.save();
          console.log('User updated:', evt.data.id);
        }
        break;

      case 'user.deleted':
        // Delete user profile when Clerk user is deleted
        await User.deleteOne({ clerkId: evt.data.id });
        console.log('User deleted:', evt.data.id);
        break;

      default:
        console.log('Unhandled webhook event:', eventType);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Webhook processing error:', error);
    return new Response('Error processing webhook', {
      status: 500,
    });
  }
}

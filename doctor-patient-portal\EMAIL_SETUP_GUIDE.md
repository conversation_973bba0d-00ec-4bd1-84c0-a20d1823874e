# 📧 Email Setup Guide for Forgot Password Feature

## 🔧 **Current Status**
- ✅ **Nodemailer**: Installed and configured
- ✅ **SMTP Settings**: Gmail SMTP configured
- ⚠️ **App Password**: Needs to be set up

## 🚀 **Quick Setup Steps**

### **Step 1: Enable 2-Factor Authentication on Gmail**
1. Go to [Google Account Settings](https://myaccount.google.com/)
2. Click **Security** in the left sidebar
3. Under **Signing in to Google**, click **2-Step Verification**
4. Follow the setup process to enable 2FA

### **Step 2: Generate Gmail App Password**
1. Go to [Google Account Settings](https://myaccount.google.com/)
2. Click **Security** → **2-Step Verification**
3. Scroll down and click **App passwords**
4. Select **Mail** and **Other (Custom name)**
5. Enter "Doctor Patient Portal" as the name
6. Click **Generate**
7. **Copy the 16-character password** (e.g., `abcd efgh ijkl mnop`)

### **Step 3: Update Environment Variables**
Open `.env.local` and update:
```env
SMTP_USER=<EMAIL>
SMTP_PASS=your-16-character-app-password-here
```

**Example:**
```env
SMTP_USER=<EMAIL>
SMTP_PASS=abcd efgh ijkl mnop
```

## 🧪 **Testing the Email Feature**

### **Method 1: Use the Forgot Password Page**
1. Go to `http://localhost:3001/forgot-password`
2. Enter your email: `<EMAIL>`
3. Click **Send Reset Link**
4. Check your Gmail inbox for the reset email

### **Method 2: Test Email API Directly**
```bash
# Test the API endpoint
curl -X POST http://localhost:3001/api/auth/forgot-password \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'
```

## 🔍 **Troubleshooting**

### **Common Issues:**

#### **1. "Invalid credentials" error**
- ✅ Make sure 2FA is enabled on your Google account
- ✅ Use App Password, not your regular Gmail password
- ✅ Remove spaces from the App Password

#### **2. "Less secure app access" error**
- ✅ Use App Password instead of enabling less secure apps
- ✅ App Passwords are the secure way to authenticate

#### **3. "Connection timeout" error**
- ✅ Check your internet connection
- ✅ Verify SMTP settings (host: smtp.gmail.com, port: 587)

#### **4. Email not received**
- ✅ Check spam/junk folder
- ✅ Verify the email address is correct
- ✅ Check server logs for error messages

## 📊 **Current Configuration**

```env
EMAIL_FROM=<EMAIL>
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-gmail-app-password-here
```

## 🎯 **Expected Behavior**

### **When Email is Configured:**
1. User enters email on forgot password page
2. System generates secure reset token
3. Email is sent via Gmail SMTP
4. User receives email with reset link
5. User clicks link to reset password

### **When Email is NOT Configured:**
1. System logs email content to console
2. User sees success message (for security)
3. Reset link is displayed in server logs
4. No actual email is sent

## 🔐 **Security Notes**

- ✅ **App Passwords** are more secure than regular passwords
- ✅ **Reset tokens** expire after 1 hour
- ✅ **Email enumeration** is prevented (always shows success)
- ✅ **HTTPS** should be used in production

## 📝 **Next Steps**

1. **Set up Gmail App Password** (5 minutes)
2. **Update .env.local** with the App Password
3. **Restart the development server**
4. **Test the forgot password feature**

Once configured, users will receive professional-looking password reset emails with secure reset links!

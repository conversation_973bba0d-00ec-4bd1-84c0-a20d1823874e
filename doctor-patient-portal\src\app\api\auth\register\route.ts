import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import connectDB from '@/lib/db';
import User from '@/models/User';
import { ApiResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const { email, password, firstName, lastName, role, phone } = await request.json();

    // Validation
    if (!email || !password || !firstName || !lastName || !role) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Email, password, first name, last name, and role are required',
      }, { status: 400 });
    }

    if (password.length < 6) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Password must be at least 6 characters long',
      }, { status: 400 });
    }

    if (!['doctor', 'patient'].includes(role)) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Role must be either doctor or patient',
      }, { status: 400 });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'User with this email already exists',
      }, { status: 400 });
    }

    // Create new user
    const user = new User({
      email: email.toLowerCase(),
      password,
      role,
      profile: {
        firstName,
        lastName,
        phone: phone || '',
      },
      isEmailVerified: false, // In production, you'd send a verification email
    });

    await user.save();

    // Generate JWT token
    const token = jwt.sign(
      { 
        userId: user._id,
        email: user.email,
        role: user.role 
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '7d' }
    );

    // Create response with user data
    const userData = {
      id: user._id,
      email: user.email,
      firstName: user.profile.firstName,
      lastName: user.profile.lastName,
      role: user.role,
      phone: user.profile.phone,
      profileImage: user.profile.profileImage,
      isEmailVerified: user.isEmailVerified,
    };

    const response = NextResponse.json<ApiResponse>({
      success: true,
      data: {
        user: userData,
        token,
      },
    });

    // Set HTTP-only cookie for additional security
    response.cookies.set('authToken', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60, // 7 days
    });

    return response;

  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Internal server error',
    }, { status: 500 });
  }
}

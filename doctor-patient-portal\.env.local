# Database
MONGODB_URI=mongodb://127.0.0.1:27017/doctor-patient-portal

# Custom Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Email Configuration
EMAIL_SERVICE=gmail
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Doctor Patient Portal

# Application URLs
APP_URL=http://localhost:3000
FRONTEND_URL=http://localhost:3000

# Cloudinary (for image uploads)
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# Email (for notifications)
EMAIL_FROM=<EMAIL>
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=wdnw iufs kpqm tprw


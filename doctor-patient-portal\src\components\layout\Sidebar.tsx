'use client';

import { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Home,
  Calendar,
  Users,
  User,
  Settings,
  LogOut,
  Menu,
  X,
  Stethoscope,
  ClipboardList,
  Search,
  Bell,
} from 'lucide-react';
import { useAuth } from '@/components/providers/AuthProvider';
import { useUIStore } from '@/lib/store';
import { toast } from 'sonner';

interface SidebarProps {
  className?: string;
}

export default function Sidebar({ className }: SidebarProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, logout } = useAuth();
  const { sidebarOpen, toggleSidebar } = useUIStore();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const isDoctor = user?.role === 'doctor';

  const doctorNavItems = [
    {
      title: 'Dashboard',
      href: '/doctor/dashboard',
      icon: Home,
    },
    {
      title: 'Appointments',
      href: '/doctor/appointments',
      icon: Calendar,
    },
    {
      title: 'Profile',
      href: '/doctor/profile',
      icon: User,
    },
    {
      title: 'Settings',
      href: '/doctor/settings',
      icon: Settings,
    },
  ];

  const patientNavItems = [
    {
      title: 'Dashboard',
      href: '/patient/dashboard',
      icon: Home,
    },
    {
      title: 'Find Doctors',
      href: '/doctors',
      icon: Search,
    },
    {
      title: 'My Appointments',
      href: '/patient/appointments',
      icon: Calendar,
    },
    {
      title: 'Profile',
      href: '/patient/profile',
      icon: User,
    },
    {
      title: 'Settings',
      href: '/patient/settings',
      icon: Settings,
    },
  ];

  const navItems = isDoctor ? doctorNavItems : patientNavItems;

  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      await logout();
      toast.success('Logged out successfully');
    } catch (error) {
      toast.error('Failed to logout');
    } finally {
      setIsLoggingOut(false);
    }
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const sidebarVariants = {
    open: {
      x: 0,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30,
      },
    },
    closed: {
      x: '-100%',
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30,
      },
    },
  };

  return (
    <>
      {/* Mobile overlay */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
            onClick={toggleSidebar}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.aside
        variants={sidebarVariants}
        animate={sidebarOpen ? 'open' : 'closed'}
        className={cn(
          'fixed left-0 top-0 z-50 h-full w-64 bg-white border-r border-gray-200 shadow-lg lg:relative lg:translate-x-0',
          className
        )}
      >
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-blue-600 rounded-lg">
                <Stethoscope className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-gray-900">HealthCare</h1>
                <p className="text-xs text-gray-500">Portal</p>
              </div>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleSidebar}
              className="lg:hidden"
            >
              <X className="w-5 h-5" />
            </Button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2">
            {navItems.map((item) => {
              const isActive = pathname === item.href;
              const Icon = item.icon;

              return (
                <motion.div
                  key={item.href}
                  whileHover={{ x: 4 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    variant={isActive ? 'default' : 'ghost'}
                    className={cn(
                      'w-full justify-start text-left',
                      isActive
                        ? 'bg-blue-600 text-white hover:bg-blue-700'
                        : 'text-gray-700 hover:bg-gray-100'
                    )}
                    onClick={() => {
                      router.push(item.href);
                      if (window.innerWidth < 1024) {
                        toggleSidebar();
                      }
                    }}
                  >
                    <Icon className="w-5 h-5 mr-3" />
                    {item.title}
                  </Button>
                </motion.div>
              );
            })}
          </nav>

          {/* User Profile */}
          <div className="p-4 border-t border-gray-200">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="w-full justify-start p-2 h-auto"
                >
                  <div className="flex items-center space-x-3">
                    <Avatar className="w-10 h-10">
                      <AvatarImage
                        src={user?.profileImage}
                        alt={`${user?.firstName || 'User'} ${user?.lastName || ''}`}
                      />
                      <AvatarFallback className="bg-blue-100 text-blue-600 font-semibold">
                        {user && getInitials(user.firstName || 'U', user.lastName || 'U')}
                      </AvatarFallback>
                    </Avatar>

                    <div className="flex-1 text-left">
                      <p className="text-sm font-medium text-gray-900">
                        {isDoctor ? 'Dr. ' : ''}{user?.firstName || user?.email?.split('@')[0] || 'User'} {user?.lastName || ''}
                      </p>
                      <p className="text-xs text-gray-500 capitalize">
                        {user?.role || 'User'}
                      </p>
                    </div>
                  </div>
                </Button>
              </DropdownMenuTrigger>
              
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuItem
                  onClick={() => router.push(isDoctor ? '/doctor/profile' : '/patient/profile')}
                >
                  <User className="mr-2 h-4 w-4" />
                  Profile
                </DropdownMenuItem>
                
                <DropdownMenuItem
                  onClick={() => router.push(isDoctor ? '/doctor/settings' : '/patient/settings')}
                >
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </DropdownMenuItem>
                
                <DropdownMenuSeparator />
                
                <DropdownMenuItem
                  onClick={handleLogout}
                  disabled={isLoggingOut}
                  className="text-red-600 focus:text-red-600"
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  {isLoggingOut ? 'Logging out...' : 'Logout'}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </motion.aside>
    </>
  );
}

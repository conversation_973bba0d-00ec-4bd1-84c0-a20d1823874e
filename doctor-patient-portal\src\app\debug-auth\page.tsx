'use client';

import { useState } from 'react';
import { useAuth } from '@/components/providers/AuthProvider';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

export default function DebugAuthPage() {
  const { user, isLoading, isAuthenticated, login, logout } = useAuth();
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('password123');
  const [loginResult, setLoginResult] = useState<any>(null);

  const handleLogin = async () => {
    console.log('Attempting login with:', { email, password });
    const result = await login(email, password);
    console.log('Login result:', result);
    setLoginResult(result);
  };

  const handleLogout = async () => {
    await logout();
    setLoginResult(null);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <h1 className="text-3xl font-bold">Authentication Debug Page</h1>
        
        {/* Auth State */}
        <Card>
          <CardHeader>
            <CardTitle>Authentication State</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><strong>Is Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
              <p><strong>Is Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
              <p><strong>User:</strong></p>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(user, null, 2)}
              </pre>
            </div>
          </CardContent>
        </Card>

        {/* Login Form */}
        <Card>
          <CardHeader>
            <CardTitle>Test Login</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
              </div>
              <div className="flex gap-4">
                <Button onClick={handleLogin} disabled={isLoading}>
                  Test Login
                </Button>
                <Button onClick={handleLogout} variant="outline">
                  Logout
                </Button>
              </div>
              {loginResult && (
                <div>
                  <p><strong>Login Result:</strong></p>
                  <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                    {JSON.stringify(loginResult, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Local Storage */}
        <Card>
          <CardHeader>
            <CardTitle>Local Storage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><strong>Auth Token:</strong></p>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                {typeof window !== 'undefined' ? localStorage.getItem('authToken') || 'No token found' : 'Server side'}
              </pre>
              <Button 
                onClick={() => {
                  if (typeof window !== 'undefined') {
                    localStorage.removeItem('authToken');
                    window.location.reload();
                  }
                }}
                variant="destructive"
                size="sm"
              >
                Clear Token
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* API Test */}
        <Card>
          <CardHeader>
            <CardTitle>Direct API Test</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button 
                onClick={async () => {
                  try {
                    const response = await fetch('/api/auth/login', {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({ email, password }),
                    });
                    const data = await response.json();
                    console.log('Direct API login result:', data);
                    alert('Check console for API result');
                  } catch (error) {
                    console.error('Direct API error:', error);
                    alert('API Error - check console');
                  }
                }}
              >
                Test Direct API Login
              </Button>
              
              <Button 
                onClick={async () => {
                  try {
                    const token = localStorage.getItem('authToken');
                    const response = await fetch('/api/auth/me', {
                      headers: { 'Authorization': `Bearer ${token}` },
                    });
                    const data = await response.json();
                    console.log('Direct API /me result:', data);
                    alert('Check console for /me result');
                  } catch (error) {
                    console.error('Direct API /me error:', error);
                    alert('/me API Error - check console');
                  }
                }}
              >
                Test /me Endpoint
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

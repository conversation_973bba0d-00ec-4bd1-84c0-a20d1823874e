'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { Calendar, Clock, Users, AlertCircle, CheckCircle, XCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import DashboardLayout from '@/components/layout/DashboardLayout';
import AppointmentCard from '@/components/dashboard/AppointmentCard';
import { AppointmentWithDetails } from '@/types';
import { useAuth } from '@/components/providers/AuthProvider';
import { toast } from 'sonner';

export default function DoctorDashboard() {
  const [pendingAppointments, setPendingAppointments] = useState<AppointmentWithDetails[]>([]);
  const [todayAppointments, setTodayAppointments] = useState<AppointmentWithDetails[]>([]);
  const [appointments, setAppointments] = useState<AppointmentWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const { user, isLoading: authLoading, isAuthenticated } = useAuth();

  const fetchAppointments = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/appointments?limit=50');
      const result = await response.json();

      if (result.success) {
        const allAppointments = result.data;
        setAppointments(allAppointments);

        // Filter pending appointments
        const pending = allAppointments.filter((apt: AppointmentWithDetails) => 
          apt.status === 'pending'
        );

        // Filter today's appointments
        const today = new Date();
        const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const todayEnd = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
        
        const todaysAppts = allAppointments.filter((apt: AppointmentWithDetails) => {
          const aptDate = new Date(apt.dateTime);
          return aptDate >= todayStart && aptDate < todayEnd && apt.status === 'approved';
        });

        setPendingAppointments(pending);
        setTodayAppointments(todaysAppts);
      } else {
        toast.error('Failed to fetch appointments');
      }
    } catch (error) {
      toast.error('Failed to fetch appointments');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!isLoaded) return;

    if (!user) {
      router.push('/sign-in');
      return;
    }

    // Check if user has completed onboarding and is a doctor
    const userRole = user.publicMetadata?.role as string;
    if (!userRole) {
      router.push('/onboarding');
      return;
    }

    if (userRole !== 'doctor') {
      router.push('/dashboard'); // Will redirect to appropriate dashboard
      return;
    }

    fetchAppointments();
  }, [isLoaded, user, router]);

  const handleStatusUpdate = async (appointmentId: string, status: string) => {
    try {
      const response = await fetch(`/api/appointments/${appointmentId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });

      if (response.ok) {
        fetchAppointments(); // Refresh appointments
      } else {
        throw new Error('Failed to update appointment');
      }
    } catch (error) {
      throw error;
    }
  };

  const getStatusCounts = () => {
    const pending = appointments.filter(apt => apt.status === 'pending').length;
    const approved = appointments.filter(apt => apt.status === 'approved').length;
    const completed = appointments.filter(apt => apt.status === 'completed').length;
    
    return { pending, approved, completed };
  };

  const statusCounts = getStatusCounts();

  const stats = [
    {
      title: 'Pending Requests',
      value: statusCounts.pending,
      icon: AlertCircle,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
    },
    {
      title: 'Approved Appointments',
      value: statusCounts.approved,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: 'Total Patients',
      value: new Set(appointments.map(apt => apt.patientId)).size,
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: 'Completed Consultations',
      value: statusCounts.completed,
      icon: CheckCircle,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
  ];

  // Show loading state while Clerk is loading
  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  // Show loading state while user data is being verified
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Verifying authentication...</p>
        </div>
      </div>
    );
  }

  return (
    <DashboardLayout
      title={`Welcome, Dr. ${user?.firstName || user?.emailAddresses[0]?.emailAddress?.split('@')[0] || 'Doctor'}!`}
      subtitle="Manage your appointments and patient consultations"
    >
      <div className="space-y-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                      <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                    </div>
                    <div className={`p-3 rounded-full ${stat.bgColor}`}>
                      <stat.icon className={`w-6 h-6 ${stat.color}`} />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button
                onClick={() => router.push('/doctor/appointments')}
                className="h-16 text-left justify-start bg-blue-600 hover:bg-blue-700"
              >
                <Calendar className="w-6 h-6 mr-3" />
                <div>
                  <div className="font-semibold">View All Appointments</div>
                  <div className="text-sm opacity-90">Manage your schedule</div>
                </div>
              </Button>
              
              <Button
                onClick={() => router.push('/doctor/profile')}
                variant="outline"
                className="h-16 text-left justify-start"
              >
                <Users className="w-6 h-6 mr-3" />
                <div>
                  <div className="font-semibold">Update Profile</div>
                  <div className="text-sm text-gray-600">Edit your information</div>
                </div>
              </Button>

              <Button
                onClick={() => router.push('/doctor/appointments?status=pending')}
                variant="outline"
                className="h-16 text-left justify-start"
              >
                <AlertCircle className="w-6 h-6 mr-3" />
                <div>
                  <div className="font-semibold">Pending Requests</div>
                  <div className="text-sm text-gray-600">
                    {statusCounts.pending} waiting for approval
                  </div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Pending Appointment Requests */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <span>Pending Requests</span>
                {statusCounts.pending > 0 && (
                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                    {statusCounts.pending}
                  </Badge>
                )}
              </CardTitle>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push('/doctor/appointments?status=pending')}
            >
              View All
            </Button>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="animate-pulse">
                    <div className="h-32 bg-gray-200 rounded-lg"></div>
                  </div>
                ))}
              </div>
            ) : pendingAppointments.length === 0 ? (
              <div className="text-center py-8">
                <CheckCircle className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No pending requests
                </h3>
                <p className="text-gray-600">
                  All appointment requests have been reviewed
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {pendingAppointments.slice(0, 3).map((appointment) => (
                  <AppointmentCard
                    key={appointment._id}
                    appointment={appointment}
                    onStatusUpdate={handleStatusUpdate}
                  />
                ))}
                {pendingAppointments.length > 3 && (
                  <div className="text-center pt-4">
                    <Button
                      variant="outline"
                      onClick={() => router.push('/doctor/appointments?status=pending')}
                    >
                      View All Pending Requests
                    </Button>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Today's Appointments */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="w-5 h-5" />
              <span>Today's Schedule</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                {Array.from({ length: 2 }).map((_, index) => (
                  <div key={index} className="animate-pulse">
                    <div className="h-32 bg-gray-200 rounded-lg"></div>
                  </div>
                ))}
              </div>
            ) : todayAppointments.length === 0 ? (
              <div className="text-center py-8">
                <Calendar className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No appointments today
                </h3>
                <p className="text-gray-600">
                  Your schedule is clear for today
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {todayAppointments.map((appointment) => (
                  <AppointmentCard
                    key={appointment._id}
                    appointment={appointment}
                    onStatusUpdate={handleStatusUpdate}
                  />
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}

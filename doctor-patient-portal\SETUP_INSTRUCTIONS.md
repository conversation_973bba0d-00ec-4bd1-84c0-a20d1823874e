# 🚀 Doctor-Patient Portal Setup Instructions

## 🎯 Quick Start Options

You have **two options** to run this application:

### Option 1: With Clerk Authentication (Recommended for Production)
### Option 2: With Original Custom Authentication (Quick Demo)

---

## 🔐 Option 1: Clerk Authentication Setup

### Step 1: Create Clerk Account
1. Go to [clerk.com](https://clerk.com) and sign up
2. Create a new application
3. Choose "Next.js" as your framework

### Step 2: Get API Keys
1. In Clerk Dashboard, go to **API Keys**
2. Copy your **Publishable Key** and **Secret Key**
3. Replace the placeholder values in `.env.local`:

```env
# Replace these with your actual Clerk keys
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_actual_key_here
CLERK_SECRET_KEY=sk_test_your_actual_secret_here
CLERK_WEBHOOK_SECRET=whsec_your_webhook_secret_here
```

### Step 3: Configure Clerk
1. **Email Settings**: Enable email verification
2. **Authentication**: Enable email/password authentication
3. **Appearance**: Customize to match your brand (optional)

### Step 4: Run Application
```bash
npm run dev
```

Visit: http://localhost:3002

---

## 🚀 Option 2: Original Authentication (Quick Demo)

If you want to quickly test the application without setting up Clerk:

### Step 1: Switch to Original Auth
Temporarily comment out Clerk in the layout:

```typescript
// src/app/layout.tsx
export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    // <ClerkProvider>  // Comment this out
      <html lang="en">
        <body className={inter.className}>
          <SocketProvider>
            {children}
          </SocketProvider>
          <Toaster />
        </body>
      </html>
    // </ClerkProvider>  // Comment this out
  );
}
```

### Step 2: Use Original Auth Pages
- **Login**: http://localhost:3002/login
- **Register**: http://localhost:3002/register
- **Forgot Password**: http://localhost:3002/forgot-password

### Step 3: Test the Application
1. **Register** a new account (doctor or patient)
2. **Login** with your credentials
3. **Explore** the role-specific dashboards
4. **Book appointments** (if patient)
5. **Manage appointments** (if doctor)

---

## 🗄️ Database Setup

### MongoDB Connection
The application is configured to use MongoDB. Make sure:

1. **MongoDB is running** on your system
2. **Connection string** is correct in `.env.local`:
   ```env
   MONGODB_URI=mongodb://127.0.0.1:27017/doctor-patient-portal
   ```

### Test Database Connection
```bash
# In the project directory
node -e "
const mongoose = require('mongoose');
mongoose.connect('mongodb://127.0.0.1:27017/doctor-patient-portal')
  .then(() => console.log('✅ MongoDB Connected'))
  .catch(err => console.log('❌ MongoDB Error:', err.message));
"
```

---

## 🎨 Features to Test

### For Patients
- ✅ **Register/Login** as a patient
- ✅ **Browse doctors** by specialty and location
- ✅ **View doctor profiles** with ratings and info
- ✅ **Book appointments** with calendar selection
- ✅ **Manage appointments** (view, cancel, reschedule)
- ✅ **Dashboard** with upcoming appointments

### For Doctors
- ✅ **Register/Login** as a doctor
- ✅ **Complete profile** with specialty and bio
- ✅ **View appointment requests** from patients
- ✅ **Approve/reject** appointment requests
- ✅ **Manage schedule** and availability
- ✅ **Dashboard** with patient statistics

### System Features
- ✅ **Real-time notifications** (Socket.io)
- ✅ **Responsive design** (mobile-friendly)
- ✅ **Role-based access** control
- ✅ **Search and filtering** for doctors
- ✅ **Email notifications** (development mode)

---

## 🔧 Troubleshooting

### Common Issues

#### "Clerk publishable key is invalid"
- You need to set up Clerk account and get real API keys
- Or use Option 2 (Original Auth) for quick testing

#### "MongoDB connection failed"
- Make sure MongoDB is running: `mongod`
- Check connection string in `.env.local`
- Try: `mongodb://127.0.0.1:27017/doctor-patient-portal`

#### "Port 3000 is in use"
- Application automatically uses port 3002
- Access at: http://localhost:3002

#### "Module not found" errors
- Run: `npm install`
- Clear cache: `rm -rf .next` and restart

### Development Commands
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

---

## 🌟 Recommended Testing Flow

### 1. Quick Demo (5 minutes)
1. Use **Option 2** (Original Auth)
2. Register as a **patient**
3. Browse and book appointment with a doctor
4. Register as a **doctor** (different email)
5. Approve the appointment request

### 2. Full Setup (15 minutes)
1. Set up **Clerk account**
2. Configure **API keys**
3. Use **Option 1** (Clerk Auth)
4. Test email verification flow
5. Explore all features

---

## 📚 Additional Resources

- **Clerk Setup**: See `CLERK_SETUP_GUIDE.md`
- **Forgot Password**: See `FORGOT_PASSWORD_GUIDE.md`
- **API Documentation**: See `README.md`
- **Project Structure**: Explore `/src` directory

---

## 🎯 Next Steps

After testing the application:

1. **Choose authentication method** (Clerk recommended)
2. **Customize branding** and colors
3. **Configure email service** for production
4. **Set up deployment** (Vercel recommended)
5. **Add additional features** as needed

The application is **production-ready** with either authentication option! 🚀

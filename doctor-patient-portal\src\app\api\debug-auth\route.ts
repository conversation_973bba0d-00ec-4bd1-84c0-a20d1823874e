import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import User from '@/models/User';

export async function GET(request: NextRequest) {
  try {
    await connectDB();
    
    // Get all users for debugging
    const users = await User.find({}).select('-password').limit(10);
    
    return NextResponse.json({
      success: true,
      data: {
        userCount: users.length,
        users: users.map(user => ({
          id: user._id,
          email: user.email,
          role: user.role,
          firstName: user.profile?.firstName,
          lastName: user.profile?.lastName,
          isEmailVerified: user.isEmailVerified,
        })),
      },
    });
    
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB();
    
    const { action, email, password } = await request.json();
    
    if (action === 'test-login') {
      // Find user
      const user = await User.findOne({ email: email.toLowerCase() });
      
      if (!user) {
        return NextResponse.json({
          success: false,
          error: 'User not found',
        }, { status: 404 });
      }
      
      // Test password
      const isPasswordValid = await user.comparePassword(password);
      
      return NextResponse.json({
        success: true,
        data: {
          userFound: true,
          passwordValid: isPasswordValid,
          user: {
            id: user._id,
            email: user.email,
            role: user.role,
            firstName: user.profile?.firstName,
            lastName: user.profile?.lastName,
          },
        },
      });
    }
    
    if (action === 'create-test-user') {
      // Delete existing test user
      await User.deleteOne({ email: '<EMAIL>' });
      
      // Create new test user
      const user = new User({
        email: '<EMAIL>',
        password: 'password123',
        role: 'patient',
        profile: {
          firstName: 'Test',
          lastName: 'User',
        },
        isEmailVerified: true,
      });
      
      await user.save();
      
      return NextResponse.json({
        success: true,
        data: {
          message: 'Test user created',
          user: {
            id: user._id,
            email: user.email,
            role: user.role,
            firstName: user.profile.firstName,
            lastName: user.profile.lastName,
          },
        },
      });
    }
    
    return NextResponse.json({
      success: false,
      error: 'Invalid action',
    }, { status: 400 });
    
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}

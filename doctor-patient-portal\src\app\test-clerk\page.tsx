'use client';

import { useUser, useClerk } from '@clerk/nextjs';
import { SignUp, SignIn } from '@clerk/nextjs';

export default function TestClerkPage() {
  const { user, isSignedIn, isLoaded } = useUser();
  const clerk = useClerk();

  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading Clerk...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8">Clerk Test Page</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Status Panel */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Clerk Status</h2>
            <div className="space-y-2">
              <p><strong>Loaded:</strong> {isLoaded ? '✅ Yes' : '❌ No'}</p>
              <p><strong>Signed In:</strong> {isSignedIn ? '✅ Yes' : '❌ No'}</p>
              <p><strong>User:</strong> {user ? user.emailAddresses[0]?.emailAddress : 'None'}</p>
              <p><strong>Clerk Instance:</strong> {clerk ? '✅ Available' : '❌ Missing'}</p>
            </div>
            
            <div className="mt-4 p-4 bg-gray-100 rounded">
              <h3 className="font-semibold mb-2">Environment Check:</h3>
              <p className="text-sm">
                <strong>Publishable Key:</strong> {process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY ? '✅ Set' : '❌ Missing'}
              </p>
              <p className="text-sm">
                <strong>Sign Up URL:</strong> {process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL || 'Not set'}
              </p>
              <p className="text-sm">
                <strong>After Sign Up URL:</strong> {process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL || 'Not set'}
              </p>
            </div>
          </div>

          {/* Sign Up Component */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Sign Up Component</h2>
            <div className="border-2 border-dashed border-gray-300 p-4 rounded">
              {!isSignedIn ? (
                <SignUp 
                  appearance={{
                    elements: {
                      card: 'shadow-none border-0',
                      rootBox: 'w-full',
                    },
                  }}
                  afterSignUpUrl="/onboarding"
                  signInUrl="/sign-in"
                />
              ) : (
                <div className="text-center py-8">
                  <p className="text-green-600 font-semibold">✅ Already signed in!</p>
                  <p className="text-sm text-gray-600 mt-2">
                    Signed in as: {user?.emailAddresses[0]?.emailAddress}
                  </p>
                  <button
                    onClick={() => clerk.signOut()}
                    className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                  >
                    Sign Out
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Sign In Component */}
        <div className="mt-8 bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Sign In Component (for comparison)</h2>
          <div className="border-2 border-dashed border-gray-300 p-4 rounded">
            {!isSignedIn ? (
              <SignIn 
                appearance={{
                  elements: {
                    card: 'shadow-none border-0',
                    rootBox: 'w-full',
                  },
                }}
                afterSignInUrl="/dashboard"
                signUpUrl="/sign-up"
              />
            ) : (
              <div className="text-center py-8">
                <p className="text-green-600 font-semibold">✅ Already signed in!</p>
              </div>
            )}
          </div>
        </div>

        <div className="mt-8 text-center">
          <a 
            href="/sign-up" 
            className="text-blue-600 hover:text-blue-800 underline mr-4"
          >
            Go to Regular Sign Up Page
          </a>
          <a 
            href="/sign-in" 
            className="text-blue-600 hover:text-blue-800 underline"
          >
            Go to Regular Sign In Page
          </a>
        </div>
      </div>
    </div>
  );
}

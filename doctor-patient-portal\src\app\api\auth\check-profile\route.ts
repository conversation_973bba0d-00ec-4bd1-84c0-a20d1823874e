import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import User from '@/models/User';
import { ApiResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const { clerkId, email } = body;

    if (!clerkId && !email) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Either clerkId or email is required',
      }, { status: 400 });
    }

    // Check if user exists
    const existingUser = await User.findOne({
      $or: [
        ...(clerkId ? [{ clerkId }] : []),
        ...(email ? [{ email }] : [])
      ]
    });

    if (existingUser) {
      return NextResponse.json<ApiResponse>({
        success: true,
        data: { 
          exists: true,
          user: {
            id: existingUser._id,
            email: existingUser.email,
            role: existingUser.role,
            clerkId: existingUser.clerkId
          }
        },
        message: 'User profile exists',
      }, { status: 200 });
    } else {
      return NextResponse.json<ApiResponse>({
        success: true,
        data: { exists: false },
        message: 'User profile does not exist',
      }, { status: 200 });
    }

  } catch (error) {
    console.error('Check profile error:', error);
    
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to check profile',
    }, { status: 500 });
  }
}

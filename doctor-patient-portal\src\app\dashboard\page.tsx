'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '@clerk/nextjs';

export default function DashboardRedirect() {
  const { user, isLoaded } = useUser();
  const router = useRouter();

  useEffect(() => {
    if (!isLoaded) return;

    if (!user) {
      router.push('/sign-in');
      return;
    }

    // Check if user has completed onboarding
    const userRole = user.publicMetadata?.role as string;

    if (!userRole) {
      router.push('/onboarding');
      return;
    }

    // Redirect based on role
    if (userRole === 'doctor') {
      router.push('/doctor/dashboard');
    } else if (userRole === 'patient') {
      router.push('/patient/dashboard');
    } else {
      router.push('/onboarding');
    }
  }, [user, isLoaded, router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Redirecting to your dashboard...</p>
      </div>
    </div>
  );
}

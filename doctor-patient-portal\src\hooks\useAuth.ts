'use client';

import { useUser } from '@clerk/nextjs';
import { useEffect, useState } from 'react';
import { User as IUser, DoctorWithUser } from '@/types';

interface AuthUser extends IUser {
  role: 'doctor' | 'patient';
}

export function useAuth() {
  const { user: clerkUser, isLoaded, isSignedIn } = useUser();
  const [dbUser, setDbUser] = useState<AuthUser | DoctorWithUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!isLoaded) return;
      
      if (!isSignedIn || !clerkUser) {
        setDbUser(null);
        setIsLoading(false);
        return;
      }

      try {
        // Check if user has completed onboarding
        const userRole = clerkUser.publicMetadata?.role as string;

        // Fetch user profile from our database
        const response = await fetch('/api/auth/profile');

        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            setDbUser(result.data);
          } else {
            // User exists in Clerk but not in our database - needs onboarding
            setDbUser(null);
          }
        } else {
          // User not found in database - needs onboarding
          setDbUser(null);
        }
      } catch (error) {
        console.error('Failed to fetch user profile:', error);
        setDbUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserProfile();
  }, [clerkUser, isLoaded, isSignedIn]);

  return {
    user: dbUser,
    clerkUser,
    isAuthenticated: isSignedIn && !!dbUser,
    isLoading: !isLoaded || isLoading,
    role: dbUser?.role || (clerkUser?.publicMetadata?.role as string),
  };
}

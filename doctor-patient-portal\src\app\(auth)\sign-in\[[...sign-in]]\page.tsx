'use client';

import { SignIn } from '@clerk/nextjs';
import { useUser, useClerk } from '@clerk/nextjs';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

// Note: metadata export removed since this is now a client component

export default function SignInPage() {
  const { user, isSignedIn, isLoaded } = useUser();
  const { signOut } = useClerk();
  const router = useRouter();

  useEffect(() => {
    console.log('🔧 SignIn page loaded');
    console.log('🔧 Clerk environment check:', {
      publishableKey: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY ? 'Set' : 'Missing',
      signInUrl: process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL,
      afterSignInUrl: process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL,
    });
    console.log('🔧 User status:', { isSignedIn, isLoaded, userEmail: user?.emailAddresses[0]?.emailAddress });
  }, [isSignedIn, isLoaded, user]);

  // If already signed in, show signed in status
  if (isSignedIn && user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 px-4 py-12">
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Already Signed In
            </h1>
            <p className="text-gray-600">
              You are currently signed in as: <strong>{user.emailAddresses[0]?.emailAddress}</strong>
            </p>
          </div>

          <div className="space-y-4">
            <button
              onClick={() => router.push('/dashboard')}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg"
            >
              Go to Dashboard
            </button>

            <button
              onClick={() => signOut()}
              className="w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg"
            >
              Sign Out
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 px-4 py-12">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome Back
          </h1>
          <p className="text-gray-600">
            Access your healthcare portal account
          </p>


        </div>

        <div className="flex justify-center">
          {isLoaded ? (
            <SignIn
              appearance={{
                elements: {
                  formButtonPrimary:
                    'bg-blue-600 hover:bg-blue-700 text-sm normal-case',
                  card: 'shadow-lg border border-gray-200 bg-white',
                  headerTitle: 'hidden',
                  headerSubtitle: 'hidden',
                  rootBox: 'w-full',
                  cardBox: 'w-full max-w-md',
                },
              }}
              afterSignInUrl="/dashboard"
              signUpUrl="/sign-up"
            />
          ) : (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading Clerk...</p>
            </div>
          )}
        </div>


      </div>
    </div>
  );
}

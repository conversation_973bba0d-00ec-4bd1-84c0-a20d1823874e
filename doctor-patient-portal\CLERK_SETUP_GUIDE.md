# Clerk Authentication Setup Guide

## Overview
The Doctor-Patient Portal now uses <PERSON> for robust email authentication and validation. Clerk provides:

- ✅ **Email Verification**: Automatic email verification on signup
- ✅ **Password Reset**: Built-in forgot password functionality
- ✅ **Social Login**: Optional Google, GitHub, etc. integration
- ✅ **Security**: Enterprise-grade security and compliance
- ✅ **User Management**: Complete user management dashboard
- ✅ **Webhooks**: Real-time user event synchronization

## 🚀 Quick Setup

### 1. Create a Clerk Account
1. Go to [clerk.com](https://clerk.com) and sign up
2. Create a new application
3. Choose "Next.js" as your framework

### 2. Get Your API Keys
From your Clerk Dashboard:
1. Go to **API Keys** section
2. Copy the **Publishable Key** and **Secret Key**
3. Go to **Webhooks** section and create a new webhook
4. Set endpoint URL to: `https://yourdomain.com/api/webhooks/clerk`
5. Subscribe to: `user.created`, `user.updated`, `user.deleted`
6. Copy the **Webhook Secret**

### 3. Update Environment Variables
Replace the placeholder values in `.env.local`:

```env
# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_actual_publishable_key
CLERK_SECRET_KEY=sk_test_your_actual_secret_key
CLERK_WEBHOOK_SECRET=whsec_your_actual_webhook_secret
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/dashboard
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/onboarding
```

### 4. Configure Clerk Dashboard
In your Clerk Dashboard:

#### Email Settings
1. Go to **Email & SMS** → **Email**
2. Enable **Email verification**
3. Customize email templates (optional)

#### Authentication Options
1. Go to **User & Authentication** → **Email, Phone, Username**
2. Enable **Email address** (required)
3. Disable **Phone number** and **Username** (optional)

#### Password Settings
1. Go to **User & Authentication** → **Password**
2. Configure password requirements
3. Enable **Forgot password** flow

#### Appearance
1. Go to **Customization** → **Appearance**
2. Customize colors to match your brand
3. Upload your logo (optional)

## 🔄 User Flow

### New User Registration
1. **User visits `/sign-up`**
2. **Enters email and password**
3. **Clerk sends verification email**
4. **User clicks verification link**
5. **Redirected to `/onboarding`**
6. **Selects role (Doctor/Patient)**
7. **Completes profile setup**
8. **Redirected to role-specific dashboard**

### Existing User Login
1. **User visits `/sign-in`**
2. **Enters credentials**
3. **Clerk validates authentication**
4. **Redirected to `/dashboard`**
5. **Dashboard redirects based on role**

### Password Reset
1. **User clicks "Forgot Password?" on sign-in**
2. **Clerk handles entire reset flow**
3. **User receives email with reset link**
4. **User sets new password**
5. **Automatically signed in**

## 🛠️ Technical Implementation

### Authentication Hook
Use the custom `useAuth` hook instead of Zustand:

```typescript
import { useAuth } from '@/hooks/useAuth';

function MyComponent() {
  const { user, isAuthenticated, isLoading, role } = useAuth();
  
  if (isLoading) return <div>Loading...</div>;
  if (!isAuthenticated) return <div>Please sign in</div>;
  
  return <div>Welcome, {user?.profile.firstName}!</div>;
}
```

### Protected Routes
Clerk middleware automatically protects routes:

```typescript
// middleware.ts handles authentication
// Public routes: /, /doctors, /sign-in, /sign-up
// Protected routes: /dashboard, /doctor/*, /patient/*
```

### API Authentication
For API routes, use Clerk's `auth()` helper:

```typescript
import { auth } from '@clerk/nextjs/server';

export async function GET() {
  const { userId } = auth();
  
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // Your API logic here
}
```

## 🔗 Integration Points

### Database Synchronization
- **Webhook**: `/api/webhooks/clerk` syncs user data
- **Profile Setup**: `/api/auth/setup-profile` creates user profiles
- **Profile Fetch**: `/api/auth/profile` gets user data

### User Metadata
Clerk stores user role in `publicMetadata`:
```typescript
await user.update({
  publicMetadata: {
    role: 'doctor' | 'patient'
  }
});
```

### Email Templates
Clerk provides customizable email templates for:
- Email verification
- Password reset
- Magic links
- Invitation emails

## 🎨 Customization

### Appearance
Customize Clerk components to match your design:

```typescript
<SignIn 
  appearance={{
    elements: {
      formButtonPrimary: 'bg-blue-600 hover:bg-blue-700',
      card: 'shadow-lg',
      headerTitle: 'hidden',
    },
  }}
/>
```

### Redirects
Configure post-authentication redirects:
- After sign-in: `/dashboard`
- After sign-up: `/onboarding`
- After sign-out: `/`

## 🔒 Security Features

### Built-in Security
- **Rate limiting**: Automatic protection against brute force
- **Email verification**: Required before account activation
- **Password policies**: Configurable strength requirements
- **Session management**: Secure JWT tokens with automatic refresh
- **CSRF protection**: Built-in cross-site request forgery protection

### Compliance
- **GDPR compliant**: Data protection and privacy controls
- **SOC 2 Type II**: Security and availability standards
- **CCPA compliant**: California Consumer Privacy Act compliance

## 🧪 Testing

### Development Testing
1. **Start the application**: `npm run dev`
2. **Visit**: http://localhost:3002/sign-up
3. **Create test account** with your email
4. **Check email** for verification link
5. **Complete onboarding** flow
6. **Test role-based access**

### Email Testing
- Use your real email for testing
- Check spam folder if emails don't arrive
- Clerk provides email logs in dashboard

## 🚀 Production Deployment

### Environment Variables
Ensure all Clerk environment variables are set in production:

```env
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_your_production_key
CLERK_SECRET_KEY=sk_live_your_production_secret
CLERK_WEBHOOK_SECRET=whsec_your_production_webhook_secret
```

### Domain Configuration
1. **Add production domain** in Clerk Dashboard
2. **Update webhook URL** to production endpoint
3. **Configure CORS** if needed

### Email Configuration
1. **Custom domain**: Set up custom email domain (optional)
2. **Email templates**: Customize for production branding
3. **Sender name**: Configure sender information

## 🔧 Troubleshooting

### Common Issues

#### "Invalid publishable key"
- Check that `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY` is correct
- Ensure you're using the right environment (test/live)

#### "Webhook verification failed"
- Verify `CLERK_WEBHOOK_SECRET` is correct
- Check webhook endpoint URL in Clerk Dashboard
- Ensure webhook is active

#### "User not found in database"
- Check webhook is properly configured
- Verify `/api/auth/setup-profile` is working
- Check database connection

#### Email not received
- Check spam folder
- Verify email settings in Clerk Dashboard
- Check Clerk email logs

### Debug Mode
Enable debug logging:
```env
CLERK_DEBUG=true
```

## 📚 Additional Resources

- [Clerk Documentation](https://clerk.com/docs)
- [Next.js Integration Guide](https://clerk.com/docs/nextjs)
- [Webhook Reference](https://clerk.com/docs/webhooks)
- [Customization Guide](https://clerk.com/docs/customization)

## 🎯 Benefits Over Custom Auth

### Developer Experience
- ✅ **No auth code to maintain**
- ✅ **Built-in security best practices**
- ✅ **Automatic updates and patches**
- ✅ **Comprehensive documentation**

### User Experience
- ✅ **Familiar sign-in/sign-up flows**
- ✅ **Reliable email delivery**
- ✅ **Mobile-responsive design**
- ✅ **Accessibility compliant**

### Security & Compliance
- ✅ **Enterprise-grade security**
- ✅ **Regular security audits**
- ✅ **Compliance certifications**
- ✅ **Automatic threat detection**

The Clerk integration provides a robust, secure, and user-friendly authentication system that handles all the complexities of modern authentication while allowing you to focus on building your healthcare application features.

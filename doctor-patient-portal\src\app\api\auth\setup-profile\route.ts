import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import connectDB from '@/lib/db';
import User from '@/models/User';
import Doctor from '@/models/Doctor';
import { ApiResponse } from '@/types';

// Helper function to authenticate user
async function authenticateUser(request: NextRequest) {
  const authHeader = request.headers.get('Authorization');
  const cookieToken = request.cookies.get('authToken')?.value;

  const token = authHeader?.replace('Bearer ', '') || cookieToken;

  if (!token) {
    throw new Error('No authentication token provided');
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;
    const user = await User.findById(decoded.userId);

    if (!user) {
      throw new Error('User not found');
    }

    return user;
  } catch (error) {
    throw new Error('Invalid or expired token');
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Profile setup API called');
    await connectDB();
    console.log('✅ Database connected');

    // Authenticate user
    let user;
    try {
      user = await authenticateUser(request);
    } catch (error) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Unauthorized',
      }, { status: 401 });
    }

    const body = await request.json();
    console.log('📝 Request body received:', JSON.stringify(body, null, 2));

    const { role, profile, doctorInfo } = body;

    // Basic validation
    if (!role) {
      console.log('❌ Validation failed - missing role');
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Missing required field: role',
      }, { status: 400 });
    }

    console.log('✅ Basic validation passed');

    // Update user profile with additional information
    console.log('🔍 Updating user profile...');

    // Update user with role and profile information
    user.role = role;
    user.profile = {
      ...user.profile,
      ...profile,

    console.log('💾 Saving user to database...');
    await user.save();
    console.log('✅ User saved successfully:', user._id);

    // If user is a doctor, create doctor profile
    if (role === 'doctor' && doctorInfo) {
      console.log('👨‍⚕️ Creating doctor profile...');
      const doctor = new Doctor({
        userId: user._id,
        specialty: doctorInfo.specialty,
        bio: doctorInfo.bio,
        experience: doctorInfo.experience,
        consultationFee: doctorInfo.consultationFee,
        location: doctorInfo.location,
        availability: [], // Will be set up later by the doctor
      });

      await doctor.save();
      console.log('✅ Doctor profile created successfully:', doctor._id);
    }

    console.log('🎉 Profile setup completed successfully!');
    return NextResponse.json<ApiResponse>({
      success: true,
      data: { user: user.toJSON() },
      message: 'Profile setup completed successfully',
    }, { status: 201 });

  } catch (error) {
    console.error('❌ Profile setup error:', error);

    // Provide more detailed error information
    const errorMessage = error instanceof Error ? error.message : 'Profile setup failed';
    const errorDetails = error instanceof Error ? error.stack : String(error);

    console.error('Error details:', errorDetails);

    return NextResponse.json<ApiResponse>({
      success: false,
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? errorDetails : undefined,
    }, { status: 500 });
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { clerkClient } from '@clerk/nextjs/server';
import connectDB from '@/lib/db';
import User from '@/models/User';
import Doctor from '@/models/Doctor';
import { ApiResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Profile setup API called');
    await connectDB();
    console.log('✅ Database connected');

    const body = await request.json();
    console.log('📝 Request body received:', JSON.stringify(body, null, 2));

    const { clerkId, email, role, profile, doctorInfo } = body;

    // Basic validation
    if (!clerkId || !email || !role) {
      console.log('❌ Validation failed - missing required fields');
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Missing required fields: clerkId, email, or role',
      }, { status: 400 });
    }

    console.log('✅ Basic validation passed');

    // Check if user already exists
    console.log('🔍 Checking for existing user...');
    const existingUser = await User.findOne({
      $or: [
        { clerkId },
        { email }
      ]
    });

    if (existingUser) {
      console.log('❌ User already exists:', existingUser.email);
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'User profile already exists',
      }, { status: 400 });
    }

    console.log('✅ No existing user found, creating new user...');

    // Create user profile
    const user = new User({
      clerkId,
      email,
      role,
      profile,
      // We don't need password field since Clerk handles authentication
    });

    console.log('💾 Saving user to database...');
    await user.save();
    console.log('✅ User saved successfully:', user._id);

    // If user is a doctor, create doctor profile
    if (role === 'doctor' && doctorInfo) {
      console.log('👨‍⚕️ Creating doctor profile...');
      const doctor = new Doctor({
        userId: user._id,
        specialty: doctorInfo.specialty,
        bio: doctorInfo.bio,
        experience: doctorInfo.experience,
        consultationFee: doctorInfo.consultationFee,
        location: doctorInfo.location,
        availability: [], // Will be set up later by the doctor
      });

      await doctor.save();
      console.log('✅ Doctor profile created successfully:', doctor._id);
    }

    // Update user metadata in Clerk
    try {
      console.log('🔄 Updating Clerk metadata...');
      const client = clerkClient();
      await client.users.updateUserMetadata(clerkId, {
        publicMetadata: {
          role: role,
        },
      });
      console.log('✅ Successfully updated Clerk metadata for user:', clerkId);
    } catch (clerkError) {
      console.error('❌ Failed to update Clerk metadata:', clerkError);
      // Continue anyway as the user profile is created
    }

    console.log('🎉 Profile setup completed successfully!');
    return NextResponse.json<ApiResponse>({
      success: true,
      data: { user: user.toJSON() },
      message: 'Profile setup completed successfully',
    }, { status: 201 });

  } catch (error) {
    console.error('❌ Profile setup error:', error);

    // Provide more detailed error information
    const errorMessage = error instanceof Error ? error.message : 'Profile setup failed';
    const errorDetails = error instanceof Error ? error.stack : String(error);

    console.error('Error details:', errorDetails);

    return NextResponse.json<ApiResponse>({
      success: false,
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? errorDetails : undefined,
    }, { status: 500 });
  }
}

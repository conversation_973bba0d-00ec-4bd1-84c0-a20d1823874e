'use client';

import { SignUp } from '@clerk/nextjs';
import { Metadata } from 'next';
import { useEffect } from 'react';

// Note: metadata export removed since this is now a client component

export default function SignUpPage() {
  useEffect(() => {
    console.log('🔧 SignUp page loaded');
    console.log('🔧 Clerk environment check:', {
      publishableKey: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY ? 'Set' : 'Missing',
      signUpUrl: process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL,
      afterSignUpUrl: process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL,
    });
  }, []);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 px-4 py-12">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Join Our Platform
          </h1>
          <p className="text-gray-600">
            Create your account to access healthcare services
          </p>

        </div>

        <div className="flex justify-center">
          <SignUp
            appearance={{
              elements: {
                formButtonPrimary:
                  'bg-blue-600 hover:bg-blue-700 text-sm normal-case',
                card: 'shadow-lg border border-gray-200 bg-white',
                headerTitle: 'hidden',
                headerSubtitle: 'hidden',
                rootBox: 'w-full',
                cardBox: 'w-full max-w-md',
              },
            }}
            afterSignUpUrl="/onboarding"
            signInUrl="/sign-in"
          />
        </div>
      </div>
    </div>
  );
}

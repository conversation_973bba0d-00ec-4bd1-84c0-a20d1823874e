import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';

const isProtectedRoute = createRouteMatcher([
  '/dashboard(.*)',
  '/doctor(.*)',
  '/patient(.*)',
  '/onboarding',
]);

// Routes that should redirect to Clerk equivalents
const authRedirects = {
  '/login': '/sign-in',
  '/register': '/sign-up',
  '/forgot-password': '/sign-in', // Clerk's sign-in has built-in forgot password
  '/reset-password': '/sign-in',
};

export default clerkMiddleware(async (auth, req) => {
  const { pathname } = req.nextUrl;

  // <PERSON>le redirects from custom auth routes to Clerk routes
  if (authRedirects[pathname as keyof typeof authRedirects]) {
    const redirectUrl = new URL(authRedirects[pathname as keyof typeof authRedirects], req.url);
    return NextResponse.redirect(redirectUrl);
  }

  // Protect routes that require authentication
  if (isProtectedRoute(req)) {
    const { userId } = await auth();
    if (!userId) {
      const signInUrl = new URL('/sign-in', req.url);
      return NextResponse.redirect(signInUrl);
    }
  }
});

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
};
